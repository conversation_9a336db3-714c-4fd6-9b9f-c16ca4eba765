/* custom dark scrollbar */
::-webkit-scrollbar {
	width: 12px;
	height: 12px;
}

::-webkit-scrollbar-track {
	background: #1f2937 !important;
}

.card-component ::-webkit-scrollbar-track {
	background: #374151 !important;
}

::-webkit-scrollbar-corner {
    background: #1f2937 !important;
}

::-webkit-scrollbar-thumb {
	min-height: 40px;
	transition: all 0.3s ease;
	border: 2px solid #1f2937;
	-webkit-transition: all 0.3s ease;
	background: linear-gradient(135deg, #6b7280, #4b5563);
}

::-webkit-scrollbar-thumb:hover {
	background: linear-gradient(135deg, #9ca3af, #6b7280);
	transform: scale(1.1);
}

/* mobile devices style */
@media screen and (max-width: 800px) {
	::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}
    ::-webkit-scrollbar-thumb {
        min-height: 40px;
    }
}

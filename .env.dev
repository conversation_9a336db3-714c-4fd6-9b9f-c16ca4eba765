# session & token encrypton key
APP_SECRET=369af56dccfce490cb9325e8b4b59a90

# define trusted domain names
TRUSTED_HOSTS=^.*$

# enable only SSL traffic (true/false)
SSL_ONLY=false

# maintenance mode config (true/false)
MAINTENANCE_MODE=false

# enable pwa (progressive web app)
PWA_APP_SUPPORT=false

# about component
ADMIN_CONTACT=<EMAIL>
AUTHOR_WEBSITE_URL=https://becvar.xyz
PROJECT_GITHUB_URL=https://github.com/lukasbecvar/admin-suite

# external APIs to use for get public ip (comma separated)
IP_APIS=https://ifconfig.me,https://api64.ipify.org,https://checkip.amazonaws.com,https://icanhazip.com

# external API for get ip information
IP_INFO_API=http://ip-api.com

# log manager config
EXTERNAL_API_LOG_TOKEN=kpHQpAECk8YXwHkm
ANTI_LOG_TOKEN=cb9325e8b4b59a90
SYSTEM_LOGS_DIR=/var/log
DATABASE_LOGGING=true
LOG_LEVEL=4 # 1: CRITICAL, 2: WARNING, 3: NOTICE, 4: INFO

# password hashing
MEMORY_COST=16384
TIME_COST=6
THREADS=4

# pagination config
LIMIT_CONTENT_PER_PAGE=100

# monitoring config
MONITORING_INTERVAL=1 # in minutes
METRICS_SAVE_INTERVAL=5 # in minutes
NETWORK_SPEED_MAX=1000 # in Mbps

# database config
DATABASE_DRIVER=pdo_mysql
DATABASE_HOST=127.0.0.1
DATABASE_PORT=3306
DATABASE_NAME=admin_suite
DATABASE_USERNAME=root
DATABASE_PASSWORD=root

# mailer config
MAILER_ENABLED=false
MAILER_HOST=smtp.seznam.cz
MAILER_PORT=465
MAILER_USERNAME=<EMAIL>
MAILER_PASSWORD=password

# push notifications config
PUSH_NOTIFICATIONS_ENABLED=false
PUSH_NOTIFICATIONS_MAX_TTL=3600 # in seconds
PUSH_NOTIFICATIONS_CONTENT_ENCODING=aesgcm
PUSH_NOTIFICATIONS_VAPID_PUBLIC_KEY=your-public-vapid-key
PUSH_NOTIFICATIONS_VAPID_PRIVATE_KEY=your-private-vapid-key

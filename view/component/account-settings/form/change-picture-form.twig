{% extends 'common/layout.twig' %}

{# ACCOUNT SETTING CHANGE PICTURE #}
{% block component %}
{# SUB NAVIGATION #}
<div class="px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
    <div class="flex items-center gap-4">
        <a href={{ path('app_account_settings_table') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to account settings">
            <i class="fas fa-arrow-left text-gray-300 text-sm"></i>
        </a>
        <div>
            <h1 class="text-l font-bold text-white">Change Profile Picture</h1>
            <p class="text-gray-400 text-sm">Update your account profile image</p>
        </div>
    </div>
</div>

{# PICTURE CHANGE FORM #}
<div class="component p-6">
    <div class="w-full max-w-md mx-auto">
        <div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-700/50 bg-gray-800/30">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-500/20 rounded flex items-center justify-center">
                        <i class="fas fa-user-circle text-blue-400 text-lg"></i>
                    </div>
                    <div>
                        <h2 class="text-xm font-bold text-white">Picture Update</h2>
                        <p class="text-gray-400 text-sm">Upload your new profile image</p>
                    </div>
                </div>
            </div>
            <div class="p-6">
                {# SUCCESS MESSAGE BOX #}
                {% for message in app.flashes('success') %}
                    <div class="bg-green-500/20 border border-green-500/30 text-green-300 p-3 mb-4 rounded text-center animate-popin">
                        <i class="fas fa-check-circle mr-2"></i>{{ message|e }}
                    </div>
                {% endfor %}

                {# ERROR MESSAGE BOX #}
                {% for message in app.flashes('error') %}
                    <div class="bg-red-500/20 border border-red-500/30 text-red-300 p-3 mb-4 rounded text-center animate-popin">
                        <i class="fas fa-exclamation-circle mr-2"></i>{{ message|e }}
                    </div>
                {% endfor %}

                {# IMAGE PREVIEW BOX #}
                <div class="image-preview mb-6 flex justify-center animate-popin">
                    <div class="relative rounded overflow-hidden border border-gray-600/50 shadow-lg bg-gray-700/30" style="display: none;" id="image-preview-container">
                        <img id="preview-image" src="#" alt="Image preview" class="w-40 h-40 object-cover">
                    </div>
                </div>

                {# CHANGE PICTURE FORM #}
                {{ form_start(profilePicChangeForm, {'attr': {'enctype': 'multipart/form-data'}}) }}
                    <div class="space-y-4">
                        {# FILE UPLOAD FIELD #}
                        <div>
                            <div class="flex justify-center">
                                <label class="cursor-pointer inline-flex items-center px-6 py-3 bg-gray-700/50 hover:bg-gray-600/50 text-white rounded transition-all duration-200 border border-gray-600/50 hover:border-gray-500/50">
                                    <i class="fas fa-upload mr-3 text-blue-400"></i>
                                    <span>Choose Image File</span>
                                    {{ form_row(profilePicChangeForm['profile-pic'], {
                                        'attr': {
                                            'class': 'hidden',
                                            'accept': 'image/jpeg,image/jpg,image/png,image/gif,image/webp',
                                            'onchange': 'validateAndPreviewImage(this)'
                                        }
                                    }) }}
                                </label>
                            </div>
                            <p class="text-gray-400 text-xs mt-2 text-center">Supported formats: JPG, PNG, GIF, WebP (max 5MB)</p>
                        </div>

                        {# FORM SUBMIT BUTTON #}
                        <button type="submit" class="w-full px-4 py-3 bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 rounded transition-all duration-200 border border-blue-500/30 font-medium">
                            <i class="fas fa-save mr-2"></i>Update Profile Picture
                        </button>
                    </div>
                {{ form_end(profilePicChangeForm) }}
            </div>
        </div>
    </div>
</div>

{# IMAGE VALIDATION AND PREVIEW FUNCTION #}
<script>
    function validateAndPreviewImage(input) {
        if (input.files && input.files[0]) {
            const file = input.files[0]
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
            const maxSize = 5 * 1024 * 1024 // 5MB in bytes

            // check file type
            if (!allowedTypes.includes(file.type)) {
                alert('Please select a valid image file (JPG, PNG, GIF, or WebP).')
                input.value = ''
                document.getElementById('image-preview-container').style.display = 'none'
                return
            }

            // check file size
            if (file.size > maxSize) {
                alert('File size must be less than 5MB.')
                input.value = ''
                document.getElementById('image-preview-container').style.display = 'none'
                return
            }

            // show preview if validation passes
            var reader = new FileReader()
            reader.onload = function(e) {
                document.getElementById('preview-image').setAttribute('src', e.target.result)
                document.getElementById('image-preview-container').style.display = 'block'
            }
            reader.readAsDataURL(file)
        } else {
            document.getElementById('image-preview-container').style.display = 'none'
        }
    }
</script>
{% endblock %}

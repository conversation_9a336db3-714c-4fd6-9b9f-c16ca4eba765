{% extends 'common/layout.twig' %}

{# DATABASE BROWSER COMPONENT - DATABASE & TABLES LIST #}
{% block component %}
<div class="flex flex-col h-full">
{# SUB-NAVIGATION #}
<div class="flex-shrink-0">
    <div class="flex items-center justify-between gap-2 border-b border-gray-700/50 bg-gray-800/30">
        <div class="flex items-center gap-3 px-2 py-1">
            {# BACK BUTTON #}
            {% if databaseName == '' %}
                <a href={{ path('app_dashboard') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to dashboard">
                    <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                </a>
            {% else %}
                <a href={{ path('app_manager_database') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to database browser">
                    <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                </a>
            {% endif %}

            <div class="hidden sm:block">
                {% if databaseName == '' %}
                    <h1 class="text-xm font-bold text-white">Database Manager</h1>
                    <p class="text-gray-400 text-xs">{{ databases|length }} databases found</p>
                {% else %}
                    <h1 class="text-xm font-bold text-white">{{ databaseName|e }}</h1>
                    <p class="text-gray-400 text-xs">{{ tables|length }} tables found</p>
                {% endif %}
            </div>
        </div>

        <div class="flex items-center gap-2 px-2 py-1">
            {# QUICK ACTIONS #}
            <a href={{ path('app_manager_database_dump') }} class="w-8 h-8 bg-green-500/20 hover:bg-green-500/30 rounded flex items-center justify-center transition-all duration-200 border border-green-500/30" title="Database dumper">
                <i class="fas fa-download text-green-400 text-xs"></i>
            </a>
            <a href={{ path('app_manager_database_console') }} class="w-8 h-8 bg-purple-500/20 hover:bg-purple-500/30 rounded flex items-center justify-center transition-all duration-200 border border-purple-500/30" title="Database query console">
                <i class="fas fa-terminal text-purple-400 text-xs"></i>
            </a>
        </div>
    </div>

    {# BREADCRUMB PANEL #}
    <div class="px-2 py-1 bg-gray-800/30 sm:border-b border-gray-700/50">
        <div class="flex items-center gap-2 text-sm">
            <i class="fas fa-database text-gray-400 text-xs"></i>
            <a href={{ path('app_manager_database') }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">Databases</a>
            {% if databaseName != '' %}
                <i class="fas fa-chevron-right text-gray-500 text-xs"></i>
                <span class="text-gray-300">{{ databaseName|e }}</span>
            {% endif %}
        </div>
    </div>

    {# MAIN CONTENT AREA #}
    <div class="flex-1 flex flex-col min-h-0">
        <div class="database-browser md:p-4 component">
            <div class="max-w-4xl mx-auto">
                <div class="overflow-x-auto">
                    {% if databaseName == '' %}
                        {# DATABASE LIST #}
                        <table class="min-w-full text-white w-full bg-gray-800/50 border border-gray-700/50 sm:rounded">
                            <thead>
                                <tr class="bg-gray-700/50">
                                    <th class="px-4 py-3 border-b border-gray-600/50 text-left font-semibold text-gray-200 whitespace-nowrap">Database</th>
                                    <th class="px-4 py-3 border-b border-gray-600/50 text-right font-semibold text-gray-200 whitespace-nowrap">Tables</th>
                                    <th class="px-4 py-3 border-b border-gray-600/50 text-right font-semibold text-gray-200 whitespace-nowrap">Size (MB)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for database in databases %}
                                    <tr class="hover:bg-gray-600/40 hover:shadow-lg hover:shadow-gray-900/20 transition-all duration-300">
                                        <td class="px-4 py-2 border-b border-gray-600/60 text-left">
                                            <a href={{ path('app_manager_database', {'database': database.name}) }} class="database-link text-blue-400 hover:text-blue-300 font-medium transition-colors duration-200">
                                                {{ database.name|e }}
                                            </a>
                                        </td>
                                        <td class="px-4 py-2 border-b border-gray-600/60 text-right text-gray-300">
                                            {{ database.table_count|e }}
                                        </td>
                                        <td class="px-4 py-2 border-b border-gray-600/60 text-right text-gray-300">
                                            {{ database.size_mb|number_format(2)|e }}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    {% else %}
                        {# TABLES LIST #}
                        {% if tables|length == 0 %}
                            <p class="flex items-center justify-center mt-20 mb-20 text-2xl text-white font-bold">
                                No tables found
                            </p>
                        {% else %}
                            <table class="min-w-full text-white w-full bg-gray-800/50 border border-gray-700/50 sm:rounded">
                                <thead>
                                    <tr class="bg-gray-700/50">
                                        <th class="px-4 py-3 border-b border-gray-600/50 text-left font-semibold text-gray-200 whitespace-nowrap">Table</th>
                                        <th class="px-4 py-3 border-b border-gray-600/50 text-right font-semibold text-gray-200 whitespace-nowrap">Rows</th>
                                        <th class="px-4 py-3 border-b border-gray-600/50 text-right font-semibold text-gray-200 whitespace-nowrap">Size (MB)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for table in tables %}
                                        <tr class="hover:bg-gray-600/40 hover:shadow-lg hover:shadow-gray-900/20 transition-all duration-300">
                                            <td class="px-4 py-2 border-b border-gray-600/60 text-left">
                                                <a href={{ path('app_manager_database_table_browser', {'database': databaseName, 'table': table.name}) }} class="database-link text-blue-400 hover:text-blue-300 font-medium transition-colors duration-200">
                                                    {{ table.name|e }}
                                                </a>
                                            </td>
                                            <td class="px-4 py-2 border-b border-gray-600/60 text-right text-gray-300">
                                                {{ table.row_count|e }}
                                            </td>
                                            <td class="px-4 py-2 border-b border-gray-600/60 text-right text-gray-300">
                                                {{ table.size_mb|number_format(2)|e }}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

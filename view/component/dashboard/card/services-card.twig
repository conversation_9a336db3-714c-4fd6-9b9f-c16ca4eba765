<div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden transition-all duration-300">
    <div class="px-2 py-2 border-b border-gray-700/50 text-gray-200 font-semibold tracking-wide text-sm uppercase flex items-center justify-between">
        <div class="flex items-center gap-2">
            <div class="w-8 h-8 bg-cyan-500/20 rounded flex items-center justify-center">
                <i class="fas fa-desktop text-cyan-400 text-sm"></i>
            </div>
            <span>Monitoring</span>
        </div>
        <a href={{ path('app_manager_monitoring') }} class="inline-flex items-center gap-1 px-2 py-1 bg-blue-500/10 hover:bg-blue-500/20 text-blue-400 hover:text-blue-300 text-xs font-semibold rounded transition-all duration-200 border border-blue-500/30" title="Go to metrics view">
            <span>View All</span>
            <i class="fas fa-external-link-alt text-xs"></i>
        </a>
    </div>
    <div class="p-[2px] overflow-y-auto max-h-[296px]">
        {# UFW SERVICE #}
        <div class="flex justify-between items-center bg-gradient-to-r from-gray-700/20 to-gray-600/20 border border-gray-600/40 rounded p-2 text-sm hover:from-gray-600/30 hover:to-gray-500/30 hover:border-gray-500/50 transition-all duration-200 mb-1 mt-1 mx-1 backdrop-blur-sm">
            <div class="flex items-center gap-3">
                <div class="flex items-center gap-2">
                    <div class="w-2 h-2 rounded-full {% if serviceManager.isUfwRunning() %}bg-green-400 shadow-lg shadow-green-400/50{% else %}bg-red-400 shadow-lg shadow-red-400/50{% endif %}"></div>
                    <span class="text-gray-200 font-medium">UFW</span>
                </div>
                <span class="px-2 py-0.5 rounded-full text-xs font-medium {% if serviceManager.isUfwRunning() %}bg-green-500/20 text-green-300 border border-green-500/30{% else %}bg-red-500/20 text-red-300 border border-red-500/30{% endif %}">
                    {% if serviceManager.isUfwRunning() %}ONLINE{% else %}OFFLINE{% endif %}
                </span>
            </div>
            <div class="flex items-center">
                {% if serviceManager.isUfwRunning() %}
                    <a href={{ path('app_action_runner', {'service': 'ufw', 'action': 'disable', 'referer': 'app_dashboard'}) }} class="bg-red-500/20 hover:bg-red-500/30 border border-red-500/40 hover:border-red-500/60 text-red-300 hover:text-red-200 px-2 py-1 rounded-md text-xs font-medium transition-all duration-200" title="Stop service">Stop</a>
                {% else %}
                    <a href={{ path('app_action_runner', {'service': 'ufw', 'action': 'enable', 'referer': 'app_dashboard'}) }} class="bg-green-500/20 hover:bg-green-500/30 border border-green-500/40 hover:border-green-500/60 text-green-300 hover:text-green-200 px-2 py-1 rounded-md text-xs font-medium transition-all duration-200" title="Start service">Start</a>
                {% endif %}
            </div>
        </div>

        {# SERVICES LIST #}
        {% for service in services %}
            {% if service.display %}
                {% if service.type == 'systemd' %}
                    {% set serviceStatus = serviceManager.isServiceRunning(service.service_name) %}
                    <div class="flex justify-between items-center bg-gradient-to-r from-gray-700/20 to-gray-600/20 border border-gray-600/40 rounded p-2 text-sm hover:from-gray-600/30 hover:to-gray-500/30 hover:border-gray-500/50 transition-all duration-200 mb-1 mt-1 mx-1 backdrop-blur-sm">
                        <div class="flex items-center gap-3">
                            <div class="flex items-center gap-2">
                                <div class="w-2 h-2 rounded-full {% if serviceStatus %}bg-green-400 shadow-lg shadow-green-400/50{% else %}bg-red-400 shadow-lg shadow-red-400/50{% endif %}"></div>
                                <a href={{ path('app_manager_monitoring_service_detail', {'service_name': service.service_name}) }} class="text-gray-200 font-medium hover:text-blue-400 truncate max-w-[140px] relative after:absolute after:bottom-0 after:left-0 after:w-0 after:h-[1px] after:bg-blue-400 hover:after:w-full after:transition-all after:duration-300">{{ service.display_name|e }}</a>
                            </div>
                            <span class="px-2 py-0.5 rounded-full text-xs font-medium {% if serviceStatus %}bg-green-500/20 text-green-300 border border-green-500/30{% else %}bg-red-500/20 text-red-300 border border-red-500/30{% endif %}">
                                {% if serviceStatus %}ONLINE{% else %}OFFLINE{% endif %}
                            </span>
                        </div>
                        <div class="flex items-center">
                            {% if serviceStatus %}
                                <a href={{ path('app_action_runner', {'service': service.service_name, 'action': 'stop', 'referer': 'app_dashboard'}) }} class="bg-red-500/20 hover:bg-red-500/30 border border-red-500/40 hover:border-red-500/60 text-red-300 hover:text-red-200 px-2 py-1 rounded-md text-xs font-medium transition-all duration-200" title="Stop service">Stop</a>
                            {% else %}
                                <a href={{ path('app_action_runner', {'service': service.service_name, 'action': 'start', 'referer': 'app_dashboard'}) }} class="bg-green-500/20 hover:bg-green-500/30 border border-green-500/40 hover:border-green-500/60 text-green-300 hover:text-green-200 px-2 py-1 rounded-md text-xs font-medium transition-all duration-200" title="Start service">Start</a>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            {% endif %}
        {% endfor %}
    </div>
</div>

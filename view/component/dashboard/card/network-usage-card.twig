<div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden transition-all duration-300 h-full flex flex-col">
    <div class="px-2 py-2 border-b border-gray-700/50 font-semibold tracking-wide text-sm uppercase text-gray-200 flex items-center justify-between">
        <span class="flex items-center gap-2">
            <div class="w-8 h-8 bg-indigo-500/20 rounded flex items-center justify-center">
                <i class="fas fa-network-wired text-indigo-400 text-sm"></i>
            </div>
            <span>Network Usage</span>
        </span>
        <a href={{ path('app_metrics_dashboard') }} class="inline-flex items-center gap-1 px-2 py-1 bg-blue-500/10 hover:bg-blue-500/20 text-blue-400 hover:text-blue-300 text-xs font-semibold rounded-md transition-all duration-200 border border-blue-500/30" title="Go to metrics view">
            <span>View All</span>
            <i class="fas fa-external-link-alt text-xs"></i>
        </a>
    </div>
    <div class="relative flex-1">
        {# LOADING STATE #}
        <div id="loading-network-stats" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div class="relative w-16 h-16">
                <div class="absolute inset-0 rounded-full border-4 border-gray-600/20"></div>
                <div class="absolute inset-0 rounded-full border-4 border-transparent border-t-indigo-400 border-r-indigo-400/60 animate-spin"></div>
                <div class="absolute inset-2 rounded-full border-3 border-transparent border-t-cyan-400 animate-spin" style="animation-duration: 1.5s; animation-direction: reverse;"></div>
                <div class="absolute inset-4 rounded-full border-2 border-transparent border-t-purple-400 animate-spin" style="animation-duration: 0.8s;"></div>
                <div class="absolute top-1/2 left-1/2 w-2 h-2 bg-indigo-400 rounded-full transform -translate-x-1/2 -translate-y-1/2 animate-pulse"></div>
            </div>
        </div>
        {# NETWORK DATA #}
        <div id="network-stats" class="hidden animate-popin flex-1 flex flex-col">
            <div class="p-[5.5px] flex-1 flex flex-col justify-between space-y-2">
                {# DOWNLOAD/UPLOAD SPEED #}
                <div class="bg-gradient-to-r from-gray-700/20 to-gray-600/20 rounded p-3 border border-gray-600/30">
                    <div class="flex items-center justify-between mb-1">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-exchange-alt text-cyan-400 text-sm"></i>
                            <span class="text-white font-medium text-sm">Transfer Speed</span>
                        </div>
                        <div class="text-right">
                            <div class="text-white font-semibold text-xs">
                                <span class="text-cyan-400">Download</span> <span id="network-usage-download">...</span>
                            </div>
                            <div class="text-white font-semibold text-xs">
                                <span class="text-green-400">Upload</span> <span id="network-usage-upload">...</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-between text-xs text-gray-400">
                        <div>Ping: <span class="text-white font-semibold" id="network-usage-ping">...</span></div>
                        <div>Interface: <span class="text-white font-semibold" id="network-usage-interface">...</span></div>
                    </div>
                </div>

                {# CONNECTION INFO #}
                <div class="bg-gradient-to-r from-gray-700/20 to-gray-600/20 rounded p-3 border border-gray-600/30">
                    <div class="flex items-center justify-between mb-1">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-globe text-blue-400 text-sm"></i>
                            <span class="text-white font-medium text-sm">Connection Info</span>
                        </div>
                        <span class="text-white font-semibold text-sm"><span class="text-xs text-gray-400">IP: </span>{{ hostServerPublicIP|e }}</span>
                    </div>
                    <div class="flex justify-between text-xs text-gray-400">
                        <div>Ping Server: <span class="text-white font-semibold" id="network-usage-ping-server">...</span></div>
                        <div>Last Check: <span class="text-white font-semibold" id="network-last-check-time">...</span></div>
                    </div>
                </div>

                {# NETWORK USAGE #}
                <div class="bg-gradient-to-r from-gray-700/20 to-gray-600/20 rounded p-3 border border-gray-600/30">
                    <div class="flex items-center justify-between mb-1">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-chart-line text-cyan-400 text-sm"></i>
                            <span class="text-white font-medium text-sm">Network Usage</span>
                        </div>
                        <span class="text-white font-semibold text-sm"><span id="network-usage">...
                    </div>
                    <div class="w-full h-3 bg-gray-600/50 rounded-full overflow-hidden">
                        <div class="h-full transition-all duration-500 ease-in-out rounded-full" id="network-progress" style="width: 0%; min-width: 0.2%; background: linear-gradient(90deg, #06b6d4, #3b82f6);"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% extends 'common/layout.twig' %}

{# MONITORING DASHBOARD #}
{% block component %}
<div class="flex flex-col">
	{# SUB-NAVIGATION #}
	<div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
		<div class="flex items-center justify-between gap-2">
			<div class="flex items-center gap-3">
				<a href={{ path('app_dashboard') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to dashboard">
					<i class="fas fa-arrow-left text-gray-300 text-xs"></i>
				</a>
				<div class="hidden sm:block">
					<h1 class="text-xm font-bold text-white">Monitoring Dashboard</h1>
					{% if lastMonitoringTime != null %}
						<p class="text-gray-400 text-xs">Last check:
							{{ lastMonitoringTime.get()|date('H:i:s') }}</p>
					{% else %}
						<p class="text-gray-400 text-xs">Service monitoring and status</p>
					{% endif %}
				</div>
			</div>
			<div class="flex items-center gap-2">
				{# ACTION BUTTONS #}
				<a href={{ path('app_manager_database_table_browser', {'database': mainDatabase, 'table': monitoringStatusTable} ) }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Database status">
					<i class="fas fa-database text-gray-300 text-xs"></i>
				</a>
				<a href={{ path('app_metrics_dashboard') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Metrics dashboard">
					<i class="fas fa-chart-line text-gray-300 text-xs"></i>
				</a>
				<a href={{ path('app_manager_monitoring_config') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Services config">
					<i class="fas fa-cog text-gray-300 text-xs"></i>
				</a>
			</div>
		</div>
	</div>

	{# MAIN CONTENT AREA #}
	<div class="flex-1 flex flex-col min-h-0">
		<div class="monitoring-manager component">
			{# SERVICES STATUS LOG - FULL WIDTH #}
			{% if monitoringLogs|length > 0 %}
				<div class="p-2">
					<div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden transition-all duration-300 mb-[-8px]">
						<div class="px-2 py-2 border-b border-gray-700/50 text-gray-200 font-semibold tracking-wide text-sm uppercase flex items-center justify-between">
							<div class="flex items-center gap-2">
								<div class="w-8 h-8 bg-yellow-500/20 rounded flex items-center justify-center">
									<i class="fas fa-list text-yellow-400 text-sm"></i>
								</div>
								<span>Status Log</span>
							</div>
							<a href={{ path('app_manager_logs') }} class="inline-flex items-center gap-1 px-2 py-1 bg-blue-500/10 hover:bg-blue-500/20 text-blue-400 hover:text-blue-300 text-xs font-semibold rounded transition-all duration-200 border border-blue-500/30" title="View all logs">
								<span>All Logs</span>
								<i class="fas fa-external-link-alt text-xs"></i>
							</a>
						</div>
						<div class="px-[2px] py-[5px] pt-[5px] overflow-y-auto max-h-[280px]">
							<div class="space-y-[5px]">
								{% for log in monitoringLogs %}
									<div class="flex items-start gap-3 p-3 bg-gradient-to-r from-gray-700/20 to-gray-600/20 border border-gray-600/40 rounded text-sm hover:from-gray-600/30 hover:to-gray-500/30 hover:border-gray-500/50 transition-all duration-200 backdrop-blur-sm mx-1">
										<div class="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0 shadow-lg shadow-yellow-400/50"></div>
										<div class="flex-1 min-w-0">
											<p class="text-gray-300 text-sm leading-relaxed">{{ log.message|e }}</p>
											<p class="text-gray-500 text-xs mt-1 font-mono">{{ log.time|date('Y-m-d H:i:s') }}</p>
										</div>
									</div>
								{% endfor %}
							</div>
						</div>
					</div>
				</div>
			{% endif %}

			<div class="grid grid-cols-1 sm:grid-cols-2 gap-2 p-2">
				{# INTERNAL SERVICES CARD #}
				<div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden transition-all duration-300">
					<div class="px-2 py-2 border-b border-gray-700/50 text-gray-200 font-semibold tracking-wide text-sm uppercase flex items-center justify-between">
						<div class="flex items-center gap-2">
							<div class="w-8 h-8 bg-green-500/20 rounded flex items-center justify-center">
								<i class="fas fa-server text-green-400 text-sm"></i>
							</div>
							<span>Internal Services</span>
						</div>
					</div>
					<div class="p-[3px] overflow-y-auto max-h-[320px]">
						{# UFW SERVICE #}
						<div class="flex justify-between items-center bg-gradient-to-r from-gray-700/20 to-gray-600/20 border border-gray-600/40 rounded p-2 text-sm hover:from-gray-600/30 hover:to-gray-500/30 hover:border-gray-500/50 transition-all duration-200 mb-1 mt-1 mx-1 backdrop-blur-sm">
							<div class="flex items-center gap-3">
			    				<div class="flex items-center gap-2">
									<div class="w-2 h-2 rounded-full {% if serviceManager.isUfwRunning() %}bg-green-400 shadow-lg shadow-green-400/50{% else %}bg-red-400 shadow-lg shadow-red-400/50{% endif %}"></div>
									<span class="text-gray-200 font-medium">UFW</span>
								</div>
								<span class="px-2 py-0.5 rounded-full text-xs font-medium {% if serviceManager.isUfwRunning() %}bg-green-500/20 text-green-300 border border-green-500/30{% else %}bg-red-500/20 text-red-300 border border-red-500/30{% endif %}">
									{% if serviceManager.isUfwRunning() %}ONLINE{% else %}OFFLINE
									{% endif %}
								</span>
							</div>
							<div class="flex items-center">
								{% if serviceManager.isUfwRunning() %}
									<a href={{ path('app_action_runner', {'service': 'ufw', 'action': 'disable', 'referer': 'app_manager_monitoring'} ) }} class="bg-red-500/20 hover:bg-red-500/30 border border-red-500/40 hover:border-red-500/60 text-red-300 hover:text-red-200 px-2 py-1 rounded-md text-xs font-medium transition-all duration-200" title="Stop service">Stop</a>
								{% else %}
									<a href={{ path('app_action_runner', {'service': 'ufw', 'action': 'enable', 'referer': 'app_manager_monitoring'} ) }} class="bg-green-500/20 hover:bg-green-500/30 border border-green-500/40 hover:border-green-500/60 text-green-300 hover:text-green-200 px-2 py-1 rounded-md text-xs font-medium transition-all duration-200" title="Start service">Start</a>
								{% endif %}
							</div>
						</div>

						{# SYSTEMD SERVICES LIST #}
						{% for service in services %}
							{% if service.display %}
								{% if service.type == 'systemd' %}
									{% set serviceStatus = serviceManager.isServiceRunning(service.service_name) %}
									<div class="flex justify-between items-center bg-gradient-to-r from-gray-700/20 to-gray-600/20 border border-gray-600/40 rounded p-2 text-sm hover:from-gray-600/30 hover:to-gray-500/30 hover:border-gray-500/50 transition-all duration-200 mb-1 mt-1 mx-1 backdrop-blur-sm">
										<div class="flex items-center gap-3">
											<div class="flex items-center gap-2">
												<div class="w-2 h-2 rounded-full {% if serviceStatus %}bg-green-400 shadow-lg shadow-green-400/50{% else %}bg-red-400 shadow-lg shadow-red-400/50{% endif %}"></div>
												<a href={{ path('app_manager_monitoring_service_detail', {'service_name': service.service_name} ) }} class="text-gray-200 font-medium hover:text-blue-400 truncate max-w-[140px] relative after:absolute after:bottom-0 after:left-0 after:w-0 after:h-[1px] after:bg-blue-400 hover:after:w-full after:transition-all after:duration-300">{{ service.display_name|e }}</a>
											</div>
											<span class="px-2 py-0.5 rounded-full text-xs font-medium {% if serviceStatus %}bg-green-500/20 text-green-300 border border-green-500/30{% else %}bg-red-500/20 text-red-300 border border-red-500/30{% endif %}">
												{% if serviceStatus %}ONLINE{% else %}OFFLINE
												{% endif %}
											</span>
										</div>
										<div class="flex items-center">
											{% if serviceStatus %}
												<a href={{ path('app_action_runner', {'service': service.service_name, 'action': 'stop', 'referer': 'app_manager_monitoring'} ) }} class="bg-red-500/20 hover:bg-red-500/30 border border-red-500/40 hover:border-red-500/60 text-red-300 hover:text-red-200 px-2 py-1 rounded-md text-xs font-medium transition-all duration-200" title="Stop service">Stop</a>
											{% else %}
												<a href={{ path('app_action_runner', {'service': service.service_name, 'action': 'start', 'referer': 'app_manager_monitoring'} ) }} class="bg-green-500/20 hover:bg-green-500/30 border border-green-500/40 hover:border-green-500/60 text-green-300 hover:text-green-200 px-2 py-1 rounded-md text-xs font-medium transition-all duration-200" title="Start service">Start</a>
											{% endif %}
										</div>
				    				</div>
								{% endif %}
							{% endif %}
						{% endfor %}
					</div>
				</div>

				{# HTTP SERVICES CARD #}
		        <div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden transition-all duration-300">
					<div class="px-2 py-2 border-b border-gray-700/50 text-gray-200 font-semibold tracking-wide text-sm uppercase flex items-center justify-between">
						<div class="flex items-center gap-2">
							<div class="w-8 h-8 bg-cyan-500/20 rounded flex items-center justify-center">
								<i class="fas fa-globe text-cyan-400 text-sm"></i>
							</div>
					    	<span>HTTP Services</span>
						</div>
						<a href={{ path('app_metrics_services_all') }} class="inline-flex items-center gap-1 px-2 py-1 bg-blue-500/10 hover:bg-blue-500/20 text-blue-400 hover:text-blue-300 text-xs font-semibold rounded transition-all duration-200 border border-blue-500/30" title="View all metrics">
							<span>All Metrics</span>
							<i class="fas fa-external-link-alt text-xs"></i>
						</a>
					</div>
					<div class="p-[3px] overflow-y-auto max-h-[320px]">
						{# HTTP SERVICES LIST #}
						{% for service in services %}
							{% if service.display %}
								{% if service.type == 'http' %}
									{# SERVICE STATUS #}
									{% set serviceStatus = serviceManager.checkWebsiteStatus(service.url) %}

							    	{# SERVICE ITEM #}
									<div class="flex justify-between items-center bg-gradient-to-r from-gray-700/20 to-gray-600/20 border border-gray-600/40 rounded p-2 text-sm hover:from-gray-600/30 hover:to-gray-500/30 hover:border-gray-500/50 transition-all duration-200 mb-1 mt-1 mx-1 backdrop-blur-sm">
										<div class="flex items-center gap-3">
											<div class="flex items-center gap-2">
												<div class="w-2 h-2 rounded-full {% if serviceStatus.responseCode in service.accept_codes %}bg-green-400 shadow-lg shadow-green-400/50{% else %}bg-red-400 shadow-lg shadow-red-400/50{% endif %}"></div>
												<a href={{ path('app_manager_monitoring_service_detail', {'service_name': service.service_name} ) }} class="text-gray-200 font-medium hover:text-blue-400 truncate max-w-[140px] relative after:absolute after:bottom-0 after:left-0 after:w-0 after:h-[1px] after:bg-blue-400 hover:after:w-full after:transition-all after:duration-300">{{ service.display_name|e }}</a>
	    									</div>
											<span class="px-2 py-0.5 rounded-full text-xs font-medium {% if serviceStatus.responseCode in service.accept_codes %}bg-green-500/20 text-green-300 border border-green-500/30{% else %}bg-red-500/20 text-red-300 border border-red-500/30{% endif %}">
												{% if serviceStatus.responseCode in service.accept_codes %}ONLINE{% else %}OFFLINE{% endif %}
											</span>
										</div>
										<div class="flex items-center">
											{% if service.metrics_monitoring.collect_metrics == true %}
												<a href={{ path('app_metrics_service', {'service_name': service.service_name} ) }} class="bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/40 hover:border-blue-500/60 text-blue-300 hover:text-blue-200 px-2 py-1 rounded-md text-xs font-medium transition-all duration-200" title="View metrics">Metrics</a>
											{% endif %}
										</div>
									</div>
								{% endif %}
							{% endif %}
						{% endfor %}
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

{# SLA HISTORY CARD #}
{% if slaHistory|length > 0 %}
	<div class="p-2 pt-1">
		<div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden transition-all duration-300">
			<div class="px-2 py-2 border-b border-gray-700/50 text-gray-200 font-semibold tracking-wide text-sm uppercase flex items-center justify-between">
	    		<div class="flex items-center gap-2">
					<div class="w-8 h-8 bg-indigo-500/20 rounded flex items-center justify-center">
						<i class="fas fa-chart-bar text-indigo-400 text-sm"></i>
					</div>
					<span>SLA History</span>
				</div>
				<a href={{ path('app_manager_monitoring_export_slahistory') }} id="loading-blocker" class="inline-flex items-center gap-1 px-2 py-1 bg-green-500/10 hover:bg-green-500/20 text-green-400 hover:text-green-300 text-xs font-semibold rounded transition-all duration-200 border border-green-500/30" title="Export SLA history">
					<span>Export</span>
					<i class="fas fa-download text-xs"></i>
				</a>
			</div>
			<div class="overflow-y-auto">
				{% for service, slaData in slaHistory %}
					<div class="bg-gray-700/30 p-2 text-sm transition">
						<div class="flex items-center justify-between mb-2">
							<a href={{ path('app_manager_monitoring_service_detail', {'service_name': service} ) }} class="text-gray-300 font-semibold hover:text-blue-400 truncate max-w-[200px] relative after:absolute after:bottom-0 after:left-0 after:w-0 after:h-[2px] after:bg-blue-400 hover:after:w-full after:transition-all after:duration-300">{{ service|e }}</a>
						</div>
						<div class="overflow-x-auto">
							<table class="w-full text-xs">
								<thead>
									<tr class="bg-gray-600/30">
										<th class="px-2 py-1 text-left text-gray-300 font-medium">Timeframe</th>
										<th class="px-2 py-1 text-right text-gray-300 font-medium">SLA</th>
									</tr>
								</thead>
								<tbody>
									{% for month, sla in slaData %}
										<tr class="border-t border-gray-600/30 hover:bg-gray-600/20 transition-colors duration-200">
											<td class="px-2 py-1 text-gray-300">{{ month|e }}</td>
											<td class="px-2 py-1 text-right font-medium">
												{% if sla < 99 %}
													<span class="text-red-400">{{ sla|e }}%</span>
												{% else %}
													<span class="text-green-400">{{ sla|e }}%</span>
												{% endif %}
											</td>
										</tr>
									{% endfor %}
								</tbody>
					    	</table>
						</div>
					</div>
				{% endfor %}
			</div>
		</div>
	</div>
{% endif %}

{% endblock %}

# docker compose configuration defines local development stack

# docker network config
networks:
    main:
        driver: bridge

services:
    # composer service for managing php dependencies
    composer:
        image: composer:latest
        container_name: admin_suite_composer
        working_dir: /app
        mem_limit: 1g
        volumes:
            - .:/app
        command: composer install --ignore-platform-reqs
    
    # node service for building assets
    node:
        image: node:latest
        container_name: admin_suite_node
        working_dir: /app
        mem_limit: 2g
        volumes:
            - .:/app
        command: npm run watch

    # database container
    mysql:
        image: mysql:latest
        container_name: admin_suite_mysql
        restart: always
        environment:
            - MYSQL_ROOT_PASSWORD=root
        volumes:
            - ./.docker/configs/mysqld.conf:/etc/mysql/mysql.conf.d/mysqld.cnf
            - ./.docker/services/mysql_database:/var/lib/mysql
            - ./.docker/services/log:/var/log
        mem_limit: 1g
        ports:
            - "3306:3306"
        networks:
            - main

    # web server container
    php:
        build:
            context: .
            dockerfile: ./.docker/Dockerfile
        container_name: admin_suite_website
        restart: always
        depends_on:
            - mysql
        environment:
            - DATABASE_HOST=mysql
        volumes:
            - ./.docker/configs/apache-site.conf:/etc/apache2/sites-available/000-default.conf
            - ./.docker/configs/php.ini:/usr/local/etc/php/php.ini
            - ./.docker/services/log:/var/log
            - ./:/var/www
        mem_limit: 1g
        ports:
            - "80:80"
        networks:
            - main
